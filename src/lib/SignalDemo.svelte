<script lang="ts">
	// 演示 Svelte 5 的 runes (反应式原语)
	
	// $state - 反应式状态
	let temperature = $state(20);
	let unit = $state<'C' | 'F'>('C');
	
	// $derived - 派生状态
	let convertedTemp = $derived(() => {
		if (unit === 'C') {
			return temperature;
		} else {
			return Math.round((temperature * 9/5) + 32);
		}
	});
	
	let tempDescription = $derived(() => {
		const temp = convertedTemp();
		if (temp < 0) return '🥶 极冷';
		if (temp < 10) return '❄️ 很冷';
		if (temp < 20) return '🌡️ 凉爽';
		if (temp < 30) return '☀️ 温暖';
		return '🔥 炎热';
	});

	// 模拟实时数据更新
	let isLive = $state(false);
	let intervalId: number | null = null;

	function toggleLiveUpdate() {
		isLive = !isLive;
		
		if (isLive) {
			intervalId = setInterval(() => {
				// 模拟温度变化
				temperature += (Math.random() - 0.5) * 4;
				temperature = Math.max(-20, Math.min(50, temperature));
				temperature = Math.round(temperature * 10) / 10;
			}, 1000);
		} else {
			if (intervalId) {
				clearInterval(intervalId);
				intervalId = null;
			}
		}
	}

	// 清理定时器
	$effect(() => {
		return () => {
			if (intervalId) {
				clearInterval(intervalId);
			}
		};
	});

	// 历史记录
	let history = $state<number[]>([]);
	
	// $effect - 副作用
	$effect(() => {
		// 当温度变化时，记录到历史中
		history.push(temperature);
		// 只保留最近10个记录
		if (history.length > 10) {
			history = history.slice(-10);
		}
	});

	let averageTemp = $derived(() => {
		if (history.length === 0) return 0;
		return Math.round((history.reduce((sum, temp) => sum + temp, 0) / history.length) * 10) / 10;
	});
</script>

<div class="signal-demo">
	<h3>🌡️ 温度监控系统</h3>
	
	<div class="controls">
		<div class="temp-control">
			<label>
				温度: 
				<input 
					type="range" 
					min="-20" 
					max="50" 
					step="0.1"
					bind:value={temperature}
					disabled={isLive}
				/>
				<span class="temp-value">
					{convertedTemp()}°{unit}
				</span>
			</label>
		</div>
		
		<div class="unit-control">
			<label>
				<input 
					type="radio" 
					bind:group={unit} 
					value="C"
				/> 摄氏度
			</label>
			<label>
				<input 
					type="radio" 
					bind:group={unit} 
					value="F"
				/> 华氏度
			</label>
		</div>
		
		<button 
			class="live-btn" 
			class:active={isLive}
			onclick={toggleLiveUpdate}
		>
			{isLive ? '停止' : '开始'} 实时更新
		</button>
	</div>

	<div class="display">
		<div class="current-temp">
			<div class="temp-circle" style="background: hsl({Math.max(0, Math.min(120, 120 - convertedTemp() * 2))}, 70%, 50%)">
				<span class="temp-number">{convertedTemp()}°</span>
				<span class="temp-unit">{unit}</span>
			</div>
			<p class="description">{tempDescription()}</p>
		</div>
		
		<div class="stats">
			<div class="stat">
				<span class="label">平均温度:</span>
				<span class="value">{averageTemp}°{unit}</span>
			</div>
			<div class="stat">
				<span class="label">记录数:</span>
				<span class="value">{history.length}</span>
			</div>
		</div>
		
		<div class="history">
			<h4>温度历史</h4>
			<div class="history-chart">
				{#each history as temp, i (i)}
					<div 
						class="history-bar"
						style="height: {Math.max(5, (temp + 20) * 2)}px; background: hsl({Math.max(0, Math.min(120, 120 - temp * 2))}, 70%, 50%)"
						title="{temp}°C"
					></div>
				{/each}
			</div>
		</div>
	</div>
</div>

<style>
	.signal-demo {
		padding: 1rem;
	}

	.controls {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		margin-bottom: 2rem;
		padding: 1rem;
		background: #f8f9fa;
		border-radius: 8px;
	}

	.temp-control {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.temp-control input[type="range"] {
		flex: 1;
		min-width: 200px;
	}

	.temp-value {
		font-weight: bold;
		font-size: 1.2rem;
		min-width: 60px;
		text-align: center;
	}

	.unit-control {
		display: flex;
		gap: 1rem;
	}

	.live-btn {
		padding: 0.5rem 1rem;
		background: #28a745;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		transition: all 0.2s;
	}

	.live-btn.active {
		background: #dc3545;
	}

	.display {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 2rem;
		align-items: start;
	}

	.current-temp {
		text-align: center;
	}

	.temp-circle {
		width: 120px;
		height: 120px;
		border-radius: 50%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin: 0 auto 1rem;
		color: white;
		font-weight: bold;
		box-shadow: 0 4px 8px rgba(0,0,0,0.2);
		transition: all 0.3s;
	}

	.temp-number {
		font-size: 2rem;
		line-height: 1;
	}

	.temp-unit {
		font-size: 1rem;
		opacity: 0.8;
	}

	.description {
		font-size: 1.2rem;
		margin: 0;
	}

	.stats {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.stat {
		display: flex;
		justify-content: space-between;
		padding: 0.5rem;
		background: #e9ecef;
		border-radius: 4px;
	}

	.label {
		font-weight: bold;
	}

	.history {
		grid-column: 1 / -1;
		margin-top: 1rem;
	}

	.history h4 {
		margin: 0 0 1rem 0;
	}

	.history-chart {
		display: flex;
		align-items: end;
		gap: 2px;
		height: 100px;
		padding: 0.5rem;
		background: #f8f9fa;
		border-radius: 4px;
		overflow-x: auto;
	}

	.history-bar {
		min-width: 20px;
		border-radius: 2px 2px 0 0;
		transition: all 0.3s;
		cursor: pointer;
	}

	.history-bar:hover {
		opacity: 0.8;
		transform: scaleY(1.1);
	}

	@media (max-width: 600px) {
		.display {
			grid-template-columns: 1fr;
		}
		
		.controls {
			align-items: stretch;
		}
		
		.temp-control {
			flex-direction: column;
			align-items: stretch;
		}
	}
</style>
