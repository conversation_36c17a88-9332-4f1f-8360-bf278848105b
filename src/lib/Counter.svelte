<script lang="ts">
	// 使用 Svelte 5 的 $state rune
	let count = $state(0);

	// 使用 $derived 计算派生状态
	let isEven = $derived(count % 2 === 0);
	let message = $derived(
		count === 0 
			? '开始计数吧!' 
			: `当前计数: ${count} (${isEven ? '偶数' : '奇数'})`
	);

	function increment() {
		count++;
	}

	function decrement() {
		count--;
	}

	function reset() {
		count = 0;
	}
</script>

<div class="counter">
	<p class="message">{message}</p>
	
	<div class="buttons">
		<button onclick={decrement} disabled={count <= 0}>-</button>
		<span class="count" class:even={isEven} class:odd={!isEven}>{count}</span>
		<button onclick={increment}>+</button>
	</div>
	
	<button class="reset" onclick={reset} disabled={count === 0}>重置</button>
</div>

<style>
	.counter {
		text-align: center;
		padding: 1rem;
	}

	.message {
		font-size: 1.1rem;
		margin-bottom: 1rem;
		color: #555;
	}

	.buttons {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 1rem;
		margin-bottom: 1rem;
	}

	button {
		padding: 0.5rem 1rem;
		font-size: 1.2rem;
		border: 2px solid #ff3e00;
		background: white;
		color: #ff3e00;
		border-radius: 4px;
		cursor: pointer;
		transition: all 0.2s;
	}

	button:hover:not(:disabled) {
		background: #ff3e00;
		color: white;
	}

	button:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.count {
		font-size: 2rem;
		font-weight: bold;
		padding: 0.5rem 1rem;
		border-radius: 4px;
		min-width: 3rem;
		display: inline-block;
		transition: all 0.3s;
	}

	.count.even {
		background: #e8f5e8;
		color: #2d5a2d;
	}

	.count.odd {
		background: #fff3e0;
		color: #8b4513;
	}

	.reset {
		background: #666;
		border-color: #666;
		color: white;
	}

	.reset:hover:not(:disabled) {
		background: #444;
		border-color: #444;
	}
</style>
