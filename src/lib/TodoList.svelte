<script lang="ts">
	interface Todo {
		id: number;
		text: string;
		completed: boolean;
	}

	// 使用 $state 管理待办事项列表
	let todos = $state<Todo[]>([
		{ id: 1, text: '学习 Svelte 5', completed: false },
		{ id: 2, text: '理解 runes 概念', completed: false },
		{ id: 3, text: '构建示例应用', completed: false }
	]);

	let newTodoText = $state('');

	// 使用 $derived 计算统计信息
	let totalTodos = $derived(todos.length);
	let completedTodos = $derived(todos.filter(todo => todo.completed).length);
	let remainingTodos = $derived(totalTodos - completedTodos);

	function addTodo() {
		if (newTodoText.trim()) {
			const newTodo: Todo = {
				id: Date.now(),
				text: newTodoText.trim(),
				completed: false
			};
			todos.push(newTodo);
			newTodoText = '';
		}
	}

	function toggleTodo(id: number) {
		const todo = todos.find(t => t.id === id);
		if (todo) {
			todo.completed = !todo.completed;
		}
	}

	function deleteTodo(id: number) {
		todos = todos.filter(t => t.id !== id);
	}

	function clearCompleted() {
		todos = todos.filter(t => !t.completed);
	}
</script>

<div class="todo-app">
	<div class="add-todo">
		<input 
			bind:value={newTodoText}
			placeholder="添加新的待办事项..."
			onkeydown={(e) => e.key === 'Enter' && addTodo()}
		/>
		<button onclick={addTodo} disabled={!newTodoText.trim()}>添加</button>
	</div>

	<div class="stats">
		<span>总计: {totalTodos}</span>
		<span>已完成: {completedTodos}</span>
		<span>剩余: {remainingTodos}</span>
	</div>

	<ul class="todo-list">
		{#each todos as todo (todo.id)}
			<li class="todo-item" class:completed={todo.completed}>
				<input 
					type="checkbox" 
					checked={todo.completed}
					onchange={() => toggleTodo(todo.id)}
				/>
				<span class="todo-text">{todo.text}</span>
				<button class="delete-btn" onclick={() => deleteTodo(todo.id)}>删除</button>
			</li>
		{/each}
	</ul>

	{#if completedTodos > 0}
		<button class="clear-completed" onclick={clearCompleted}>
			清除已完成 ({completedTodos})
		</button>
	{/if}
</div>

<style>
	.todo-app {
		max-width: 500px;
		margin: 0 auto;
	}

	.add-todo {
		display: flex;
		gap: 0.5rem;
		margin-bottom: 1rem;
	}

	.add-todo input {
		flex: 1;
		padding: 0.5rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
	}

	.add-todo button {
		padding: 0.5rem 1rem;
		background: #ff3e00;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.add-todo button:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.stats {
		display: flex;
		justify-content: space-between;
		margin-bottom: 1rem;
		padding: 0.5rem;
		background: #f0f0f0;
		border-radius: 4px;
		font-size: 0.9rem;
	}

	.todo-list {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	.todo-item {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.75rem;
		border: 1px solid #eee;
		border-radius: 4px;
		margin-bottom: 0.5rem;
		background: white;
		transition: all 0.2s;
	}

	.todo-item.completed {
		opacity: 0.6;
		background: #f9f9f9;
	}

	.todo-item.completed .todo-text {
		text-decoration: line-through;
		color: #888;
	}

	.todo-text {
		flex: 1;
		font-size: 1rem;
	}

	.delete-btn {
		padding: 0.25rem 0.5rem;
		background: #dc3545;
		color: white;
		border: none;
		border-radius: 3px;
		cursor: pointer;
		font-size: 0.8rem;
	}

	.delete-btn:hover {
		background: #c82333;
	}

	.clear-completed {
		width: 100%;
		padding: 0.5rem;
		background: #6c757d;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		margin-top: 1rem;
	}

	.clear-completed:hover {
		background: #5a6268;
	}
</style>
