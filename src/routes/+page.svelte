<script lang="ts">
	import Counter from '$lib/Counter.svelte';
	import TodoList from '$lib/TodoList.svelte';
	import SignalDemo from '$lib/SignalDemo.svelte';

	let name = $state('Svelte 5');
</script>

<main>
	<h1>欢迎使用 {name}!</h1>

	<section class="demo-section">
		<h2>🔢 计数器组件</h2>
		<Counter />
	</section>

	<section class="demo-section">
		<h2>📝 待办事项列表</h2>
		<TodoList />
	</section>

	<section class="demo-section">
		<h2>⚡ Signals 演示</h2>
		<SignalDemo />
	</section>
</main>

<style>
	main {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}

	h1 {
		color: #ff3e00;
		text-align: center;
		margin-bottom: 2rem;
	}

	.demo-section {
		margin: 2rem 0;
		padding: 1.5rem;
		border: 1px solid #e0e0e0;
		border-radius: 8px;
		background: #f9f9f9;
	}

	.demo-section h2 {
		margin-top: 0;
		color: #333;
	}
</style>
